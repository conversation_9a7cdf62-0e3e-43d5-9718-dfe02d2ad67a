/**
 * LiveKit JavaScript Service for Windows/macOS platforms
 * Provides the same functionality as the Rust implementation using livekit-client
 */

import {
  Room,
  RoomEvent,
  Track,
  RemoteTrack,
  RemoteParticipant,
  ParticipantEvent,
  ConnectionState,
  DisconnectReason,
  type RoomOptions
} from 'livekit-client';
import { LiveKitTokenService, type LiveKitTokenParams } from './LiveKitTokenService';

export interface ConnectionStatus {
  connected: boolean;
  room_name?: string;
  participant_count?: number;
}

export interface LiveKitParticipant {
  identity: string;
  name: string;
  metadata?: string;
  is_local: boolean;
  audio_published: boolean;
  video_published: boolean;
}

export type LiveKitEventCallback = (event: string, data: any) => void;

export class LiveKitJSService {
  private static readonly LIVEKIT_URL = 'wss://livekit.newhorizonco.uk';
  
  private room: Room | null = null;
  private isConnected = false;
  private isAudioPublishing = false;
  private eventCallbacks: LiveKitEventCallback[] = [];

  /**
   * Initialize LiveKit events (matches Rust init_livekit_events)
   */
  async initEvents(): Promise<void> {
    console.log('🔧 [LIVEKIT_JS] Initializing LiveKit events...');
    // Events will be set up when room is connected
  }

  /**
   * Get LiveKit token (matches Rust get_livekit_token)
   */
  async getToken(params: LiveKitTokenParams): Promise<string> {
    return await LiveKitTokenService.getToken(params);
  }

  /**
   * Connect to LiveKit room (matches Rust connect_to_room)
   */
  async connectToRoom(token: string, roomName: string): Promise<ConnectionStatus> {
    console.log('🔗 [LIVEKIT_JS] Attempting to connect to room:', roomName);

    if (token.trim().length === 0) {
      throw new Error('Token cannot be empty');
    }
    if (roomName.trim().length === 0) {
      throw new Error('Room name cannot be empty');
    }

    // Disconnect from existing room if connected
    if (this.isConnected && this.room) {
      console.log('⚠️ [LIVEKIT_JS] Already connected to a room, disconnecting first...');
      await this.disconnectFromRoom();
    }

    console.log('🌐 [LIVEKIT_JS] Connecting to LiveKit server:', LiveKitJSService.LIVEKIT_URL);

    try {
      // Create room instance
      this.room = new Room({
        adaptiveStream: true,
        dynacast: true,
        publishDefaults: {
          audioPreset: {
            maxBitrate: 20_000,
          },
        },
      } as RoomOptions);

      // Set up event listeners
      this.setupRoomEventListeners();

      // Connect to room
      await this.room.connect(LiveKitJSService.LIVEKIT_URL, token);
      
      this.isConnected = true;
      console.log('✅ [LIVEKIT_JS] Connected to room:', this.room.name);

      // Automatically start audio publishing (matches Rust behavior)
      await this.startAudioPublishing();

      return {
        connected: true,
        room_name: this.room.name,
        participant_count: this.room.numParticipants,
      };
    } catch (error) {
      const errorMsg = `Failed to connect to room: ${error}`;
      console.error('❌ [LIVEKIT_JS]', errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * Disconnect from LiveKit room (matches Rust disconnect_from_room)
   */
  async disconnectFromRoom(): Promise<void> {
    console.log('🔌 [LIVEKIT_JS] Disconnecting from room...');

    try {
      // Stop audio publishing first
      if (this.isAudioPublishing) {
        await this.stopAudioPublishing();
      }

      // Disconnect room
      if (this.room) {
        await this.room.disconnect();
        this.room = null;
      }

      this.isConnected = false;
      console.log('✅ [LIVEKIT_JS] Disconnected from room successfully');
    } catch (error) {
      console.error('❌ [LIVEKIT_JS] Error during disconnect:', error);
      throw new Error(`Failed to disconnect: ${error}`);
    }
  }

  /**
   * Start audio publishing (matches Rust start_audio_publishing)
   */
  async startAudioPublishing(): Promise<void> {
    console.log('🎤 [LIVEKIT_JS] Starting audio publishing...');

    if (!this.room || !this.isConnected) {
      throw new Error('Not connected to room');
    }

    if (this.isAudioPublishing) {
      console.log('⚠️ [LIVEKIT_JS] Audio already publishing');
      return;
    }

    try {
      // Enable microphone using the standard API
      await this.room.localParticipant.setMicrophoneEnabled(true);

      this.isAudioPublishing = true;
      console.log('✅ [LIVEKIT_JS] Audio track published successfully');
    } catch (error) {
      const errorMsg = `Failed to start audio publishing: ${error}`;
      console.error('❌ [LIVEKIT_JS]', errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * Stop audio publishing (matches Rust stop_audio_publishing)
   */
  async stopAudioPublishing(): Promise<void> {
    console.log('🔇 [LIVEKIT_JS] Stopping audio publishing...');

    if (!this.isAudioPublishing) {
      console.log('⚠️ [LIVEKIT_JS] Audio not currently publishing');
      return;
    }

    try {
      if (this.room) {
        // Disable microphone using the standard API
        await this.room.localParticipant.setMicrophoneEnabled(false);
      }

      this.isAudioPublishing = false;
      console.log('✅ [LIVEKIT_JS] Audio publishing stopped');
    } catch (error) {
      console.error('❌ [LIVEKIT_JS] Error stopping audio publishing:', error);
      throw new Error(`Failed to stop audio publishing: ${error}`);
    }
  }

  /**
   * Mute remote audio (matches Rust mute_remote_audio)
   */
  async muteRemoteAudio(): Promise<void> {
    console.log('🔇 [LIVEKIT_JS] Muting remote audio...');

    if (!this.room) {
      throw new Error('Not connected to room');
    }

    try {
      // Mute all remote audio tracks by setting volume to 0
      this.room.remoteParticipants.forEach((participant) => {
        participant.audioTrackPublications.forEach((publication) => {
          if (publication.track && publication.track.mediaStreamTrack) {
            const audioElement = publication.track.mediaStreamTrack;
            // Store original volume and mute
            (audioElement as any)._originalVolume = (audioElement as any).volume || 1;
            if (publication.track.attachedElements) {
              publication.track.attachedElements.forEach((element: HTMLAudioElement) => {
                element.volume = 0;
              });
            }
          }
        });
      });

      console.log('✅ [LIVEKIT_JS] Remote audio muted');
    } catch (error) {
      console.error('❌ [LIVEKIT_JS] Error muting remote audio:', error);
      throw new Error(`Failed to mute remote audio: ${error}`);
    }
  }

  /**
   * Unmute remote audio (matches Rust unmute_remote_audio)
   */
  async unmuteRemoteAudio(): Promise<void> {
    console.log('🔊 [LIVEKIT_JS] Unmuting remote audio...');

    if (!this.room) {
      throw new Error('Not connected to room');
    }

    try {
      // Unmute all remote audio tracks by restoring volume
      this.room.remoteParticipants.forEach((participant) => {
        participant.audioTrackPublications.forEach((publication) => {
          if (publication.track && publication.track.mediaStreamTrack) {
            if (publication.track.attachedElements) {
              publication.track.attachedElements.forEach((element: HTMLAudioElement) => {
                element.volume = (element as any)._originalVolume || 1;
              });
            }
          }
        });
      });

      console.log('✅ [LIVEKIT_JS] Remote audio unmuted');
    } catch (error) {
      console.error('❌ [LIVEKIT_JS] Error unmuting remote audio:', error);
      throw new Error(`Failed to unmute remote audio: ${error}`);
    }
  }

  /**
   * Get connection status (matches Rust get_connection_status)
   */
  getConnectionStatus(): ConnectionStatus {
    return {
      connected: this.isConnected,
      room_name: this.room?.name,
      participant_count: this.room?.numParticipants || 0,
    };
  }

  /**
   * Get room participants (matches Rust get_room_participants)
   */
  getRoomParticipants(): LiveKitParticipant[] {
    if (!this.room) {
      return [];
    }

    const participants: LiveKitParticipant[] = [];

    // Add local participant
    const localParticipant = this.room.localParticipant;
    participants.push({
      identity: localParticipant.identity,
      name: localParticipant.name || localParticipant.identity,
      metadata: localParticipant.metadata,
      is_local: true,
      audio_published: localParticipant.isMicrophoneEnabled,
      video_published: localParticipant.isCameraEnabled,
    });

    // Add remote participants
    this.room.remoteParticipants.forEach((participant) => {
      participants.push({
        identity: participant.identity,
        name: participant.name || participant.identity,
        metadata: participant.metadata,
        is_local: false,
        audio_published: participant.isMicrophoneEnabled,
        video_published: participant.isCameraEnabled,
      });
    });

    return participants;
  }

  /**
   * Get audio publish status (matches Rust get_audio_publish_status)
   */
  getAudioPublishStatus(): boolean {
    return this.isAudioPublishing;
  }

  /**
   * Add event callback
   */
  addEventListener(callback: LiveKitEventCallback): void {
    this.eventCallbacks.push(callback);
  }

  /**
   * Remove event callback
   */
  removeEventListener(callback: LiveKitEventCallback): void {
    const index = this.eventCallbacks.indexOf(callback);
    if (index > -1) {
      this.eventCallbacks.splice(index, 1);
    }
  }

  /**
   * Setup room event listeners
   */
  private setupRoomEventListeners(): void {
    if (!this.room) return;

    // Participant joined
    this.room.on(RoomEvent.ParticipantConnected, (participant: RemoteParticipant) => {
      console.log('👤 [LIVEKIT_JS] Participant joined:', participant.identity);
      this.notifyEventCallbacks('participant_joined', {
        identity: participant.identity,
        name: participant.name || participant.identity,
      });

      // Subscribe to participant's audio tracks
      participant.on(ParticipantEvent.TrackSubscribed, (track: RemoteTrack) => {
        if (track.kind === Track.Kind.Audio) {
          console.log('🔊 [LIVEKIT_JS] Subscribed to audio track from:', participant.identity);
          // Audio will play automatically
        }
      });
    });

    // Participant left
    this.room.on(RoomEvent.ParticipantDisconnected, (participant: RemoteParticipant) => {
      console.log('👋 [LIVEKIT_JS] Participant left:', participant.identity);
      this.notifyEventCallbacks('participant_left', {
        identity: participant.identity,
      });
    });

    // Connection state changed
    this.room.on(RoomEvent.ConnectionStateChanged, (state: ConnectionState) => {
      console.log('🔗 [LIVEKIT_JS] Connection state changed:', state);
      this.notifyEventCallbacks('connection_state_changed', { state });
    });

    // Disconnected
    this.room.on(RoomEvent.Disconnected, (reason?: DisconnectReason) => {
      console.log('🔌 [LIVEKIT_JS] Disconnected from room:', reason);
      this.isConnected = false;
      this.notifyEventCallbacks('disconnected', { reason });
    });
  }

  /**
   * Notify all event callbacks
   */
  private notifyEventCallbacks(event: string, data: any): void {
    this.eventCallbacks.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('❌ [LIVEKIT_JS] Error in event callback:', error);
      }
    });
  }

  /**
   * Emergency cleanup (matches Rust emergency_livekit_cleanup)
   */
  async emergencyCleanup(): Promise<void> {
    console.log('🚨 [LIVEKIT_JS] Emergency cleanup...');
    
    try {
      await this.disconnectFromRoom();
    } catch (error) {
      console.error('❌ [LIVEKIT_JS] Error during emergency cleanup:', error);
    }
  }
}
